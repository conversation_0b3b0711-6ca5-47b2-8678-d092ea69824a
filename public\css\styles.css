/* General Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --accent-color: #ff6b6b;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

body {
    font-family: 'Inter', 'Montserrat', sans-serif;
    line-height: 1.6;
    color: #333;
    scroll-behavior: smooth;
}

a {
    text-decoration: none;
    transition: all 0.3s ease;
}

.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

/* Smooth scrolling offset for fixed navbar */
html {
    scroll-padding-top: 80px;
}

/* Navigation */
.navbar {
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    padding: 1rem 0;
}

.navbar.navbar-scrolled {
    background-color: rgba(255, 255, 255, 0.95) !important;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-family: 'Montserrat', sans-serif;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color) !important;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    transform: scale(1.05);
}

.navbar-nav .nav-link {
    font-weight: 500;
    color: #333 !important;
    margin: 0 0.5rem;
    padding: 0.5rem 1rem !important;
    border-radius: 25px;
    transition: all 0.3s ease;
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
    background-color: rgba(13, 110, 253, 0.1);
    transform: translateY(-2px);
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background-color: var(--primary-color);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after {
    width: 80%;
}

.navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
}

.navbar-toggler:focus {
    box-shadow: none;
}

/* Banner Section */
.banner {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    padding-top: 80px;
}

.banner-enhanced {
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.9) 0%,
        rgba(118, 75, 162, 0.9) 50%,
        rgba(255, 107, 107, 0.8) 100%),
        url('../images/complaint-banner.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    animation: gradientShift 20s ease infinite;
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.banner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
}

.banner-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    animation: float 6s ease-in-out infinite;
    z-index: 1;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.banner-content {
    flex: 1;
    padding: 5% 8%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    color: white;
    z-index: 2;
    max-width: 800px;
}

.banner-title {
    font-family: 'Montserrat', sans-serif;
    font-size: 4rem;
    font-weight: 800;
    margin-bottom: 1rem;
    text-transform: uppercase;
    text-shadow: 2px 2px 8px rgba(0,0,0,0.3);
    letter-spacing: 2px;
    position: relative;
    display: inline-block;
    color: #ffffff;
    background: linear-gradient(45deg, #ffffff, #f8f9fa);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.banner-title-wrapper {
    position: relative;
    margin-bottom: 2rem;
}

.banner-underline {
    width: 100px;
    height: 5px;
    background: linear-gradient(90deg, var(--accent-color), #ffc107);
    border-radius: 3px;
    position: relative;
    box-shadow: 0 2px 10px rgba(255, 107, 107, 0.4);
}

@keyframes expandWidth {
    from { width: 0; }
    to { width: 80px; }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.banner-subtitle {
    font-family: 'Inter', sans-serif;
    font-size: 1.4rem;
    margin-bottom: 3rem;
    font-weight: 400;
    text-shadow: 1px 1px 4px rgba(0,0,0,0.3);
    color: rgba(255, 255, 255, 0.95);
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    letter-spacing: 0.5px;
    line-height: 1.8;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.feature-card {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 12px 15px;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
    animation: fadeInUp 1.2s ease-out;
    animation-fill-mode: both;
}

.feature-card:nth-child(1) {
    animation-delay: 0.3s;
}

.feature-card:nth-child(2) {
    animation-delay: 0.5s;
}

.feature-card:nth-child(3) {
    animation-delay: 0.7s;
}

.feature-card:hover {
    transform: translateY(-5px);
    background-color: rgba(255, 255, 255, 0.15);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 32px;
    height: 32px;
    background-color: #ff6b6b;
    border-radius: 50%;
    margin-right: 12px;
    font-weight: bold;
    box-shadow: 0 3px 6px rgba(255, 107, 107, 0.3);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-text {
    font-weight: 500;
    letter-spacing: 0.5px;
}

.feature-card:hover .feature-icon {
    transform: rotate(360deg);
    box-shadow: 0 5px 10px rgba(255, 107, 107, 0.4);
    transition: transform 0.5s ease, box-shadow 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.banner-buttons {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
    animation: fadeInUp 1.5s ease-out;
    animation-delay: 0.8s;
    animation-fill-mode: both;
}

.btn-primary {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: #ff6b6b;
    color: white;
    padding: 1rem 2rem;
    border-radius: 30px;
    font-weight: bold;
    font-family: 'Montserrat', sans-serif;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
    position: relative;
    overflow: hidden;
    border: none;
    text-decoration: none;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.7s ease;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    background-color: #ff5252;
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(255, 107, 107, 0.5);
}

.btn-secondary {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    color: white;
    padding: 0.9rem 2rem;
    border-radius: 30px;
    font-weight: bold;
    font-family: 'Montserrat', sans-serif;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    border: 2px solid white;
    position: relative;
    z-index: 1;
    text-decoration: none;
}

.btn-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.15);
    transition: width 0.3s ease;
    z-index: -1;
    border-radius: 28px;
}

.btn-secondary:hover::before {
    width: 100%;
}

.btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.8);
}

.arrow {
    margin-left: 8px;
    font-size: 1.2rem;
    transition: transform 0.3s ease;
    display: inline-block;
}

.btn-primary:hover .arrow {
    transform: translateX(8px);
    animation: arrowPulse 1.5s infinite;
}

@keyframes arrowPulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
        transform: translateX(5px);
    }
    100% {
        opacity: 1;
        transform: translateX(8px);
    }
}

.banner-image {
    flex: 1;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

.banner-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

/* For mobile devices, make the banner stack vertically */
@media (max-width: 768px) {
    .banner {
        flex-direction: column;
    }
    
    .banner-content {
        padding: 15% 8%;
    }
    
    .banner-image {
        min-height: 300px;
    }
    
    .banner h1 {
        font-size: 2.5rem;
    }
    
    .banner-subtitle {
        font-size: 1.3rem;
    }
    
    .feature-item {
        font-size: 1.1rem;
    }
    
    .banner-buttons {
        flex-direction: column;
        gap: 1rem;
    }
    
    .btn-primary, .btn-secondary {
        width: 100%;
        justify-content: center;
    }
}

/* Services Section */
.services {
    padding: 4rem 5%;
    text-align: center;
}

.services h2 {
    font-size: 2rem;
    margin-bottom: 2rem;
    color: #333;
}

.service-cards {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 2rem;
}

/* Contact Section */
.Contact {
    padding: 5rem 0;
    background-color: #f8f9fa;
}

.section-title {
    font-family: 'Montserrat', sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1.5px;
    color: #333;
    text-align: center;
    position: relative;
}

.title-underline {
    width: 80px;
    height: 4px;
    background-color: #ff6b6b;
    border-radius: 2px;
    margin: 0 auto;
}

.contact-text {
    font-size: 1.1rem;
    color: #555;
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.8;
}

.contact-info {
    margin-top: 2rem;
}

.contact-item {
    padding: 1.5rem;
    transition: all 0.3s ease;
    border-radius: 10px;
}

.contact-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

.contact-icon {
    color: #ff6b6b;
    margin-bottom: 1rem;
}

.contact-detail {
    font-size: 1.2rem;
    font-weight: 500;
    color: #333;
    margin-bottom: 0;
}

.phone-number {
    font-weight: 700;
}

/* Stats Section */
.stat-item {
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    font-family: 'Montserrat', sans-serif;
}

.stat-label {
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Services Section */
.service-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(13, 110, 253, 0.05), transparent);
    transition: left 0.6s ease;
}

.service-card:hover::before {
    left: 100%;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

.service-icon {
    transition: all 0.3s ease;
}

.service-card:hover .service-icon {
    transform: scale(1.1) rotate(5deg);
}

.service-title {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
}

.service-description {
    line-height: 1.7;
}

.service-features li {
    padding: 0.25rem 0;
    font-size: 0.9rem;
}

/* About Section */
.about-feature {
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.about-feature:hover {
    transform: translateX(10px);
}

.feature-icon-small {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.about-image-wrapper {
    position: relative;
}

.about-image {
    position: relative;
    overflow: hidden;
}

.about-image::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Testimonials Section */
.testimonial-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.testimonial-rating {
    font-size: 0.9rem;
}

.testimonial-text {
    font-size: 1rem;
    line-height: 1.7;
    color: #555;
}

.author-avatar {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
}

.testimonial-author h6 {
    font-weight: 600;
    color: #333;
}

/* CTA Box */
.cta-box {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    position: relative;
    overflow: hidden;
}

.cta-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
    background-size: 50px 50px;
    opacity: 0.3;
}

/* Footer */
footer {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    position: relative;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.social-links a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.social-links a:hover {
    background-color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(13, 110, 253, 0.4);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .banner-title {
        font-size: 3.5rem;
    }

    .banner-subtitle {
        font-size: 1.3rem;
    }
}

@media (max-width: 992px) {
    .banner {
        min-height: 90vh;
        padding-top: 100px;
    }

    .banner-title {
        font-size: 3rem;
    }

    .banner-subtitle {
        font-size: 1.2rem;
    }

    .feature-card {
        margin-bottom: 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .banner {
        min-height: 80vh;
        padding-top: 80px;
    }

    .banner-title {
        font-size: 2.5rem;
        letter-spacing: 1px;
    }

    .banner-subtitle {
        font-size: 1.1rem;
        margin-bottom: 2rem;
    }

    .banner-underline {
        width: 80px;
        height: 4px;
    }

    .feature-card {
        flex-direction: column;
        text-align: center;
        padding: 1.5rem 1rem;
    }

    .feature-icon {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }

    .banner-buttons {
        flex-direction: column;
        gap: 1rem;
    }

    .btn-primary, .btn-secondary {
        width: 100%;
        justify-content: center;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .service-card, .testimonial-card {
        margin-bottom: 2rem;
    }

    .navbar-nav {
        text-align: center;
        padding-top: 1rem;
    }

    .navbar-nav .nav-link {
        margin: 0.25rem 0;
    }
}

@media (max-width: 576px) {
    .banner-title {
        font-size: 2rem;
    }

    .banner-subtitle {
        font-size: 1rem;
    }

    .btn-primary, .btn-secondary {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .service-card, .testimonial-card, .contact-item {
        padding: 1.5rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }
}

/* Loading Animation */
.loading {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.loading.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* Scroll to top button */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 1.2rem;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 0 4px 15px rgba(13, 110, 253, 0.3);
}

.scroll-to-top.show {
    opacity: 1;
    visibility: visible;
}

.scroll-to-top:hover {
    background-color: #0056b3;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(13, 110, 253, 0.4);
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0056b3;
}

/* Utility classes */
.text-gradient {
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-custom {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.border-gradient {
    border: 2px solid;
    border-image: linear-gradient(45deg, var(--primary-color), var(--accent-color)) 1;
}
